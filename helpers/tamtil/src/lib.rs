//! # TAMTIL: Zero-Copy Actor System
//!
//! A production-ready actor system built on rkyv's zero-copy serialization,
//! following <PERSON>'s actor pattern with action->reaction and remember->recall.

// Re-export all modules
pub mod platform;
pub mod context;
pub mod actor;
pub mod memories;
pub mod messenger;

// Re-export core types
pub use platform::*;
pub use context::*;
pub use actor::*;
pub use memories::*;
pub use messenger::*;

use rkyv::{Archive, Deserialize, Serialize};
use std::fmt;
use thiserror::Error;

/// TAMTIL's error system is designed for both development and production.
/// Each error provides enough context for debugging while being efficient.
#[derive(Error, Debug, Clone)]
pub enum TamtilError {
    #[error("Actor not found: {id}")]
    ActorNotFound { id: String },

    #[error("Storage error: {message}")]
    Storage { message: String },

    #[error("Communication error: {message}")]
    Communication { message: String },

    #[error("Serialization failed: {message}")]
    Serialization { message: String },

    #[error("Deserialization failed: {message}")]
    Deserialization { message: String },

    #[error("Invalid actor address: {address}")]
    InvalidAddress { address: String },

    #[error("Actor {id} panicked: {panic_message}")]
    ActorPanic { id: String, panic_message: String },

    #[error("Memory corruption detected at offset {offset}")]
    MemoryCorruption { offset: u64 },
}

/// Result type for all TAMTIL operations
pub type TamtilResult<T> = Result<T, TamtilError>;

/// Actor addresses follow URL patterns: platform.com/context/actor/child
/// This enables natural distribution and fault isolation.
#[derive(Debug, Clone, PartialEq, Eq, Hash, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub struct ActorId(String);

impl ActorId {
    /// Create a new actor ID
    pub fn new(id: impl Into<String>) -> Self {
        Self(id.into())
    }

    /// Get the string representation
    pub fn as_str(&self) -> &str {
        &self.0
    }

    /// Create a child actor ID
    pub fn child(&self, name: impl Into<String>) -> Self {
        Self(format!("{}/{}", self.0, name.into()))
    }

    /// Get the parent actor ID
    pub fn parent(&self) -> Option<Self> {
        self.0.rfind('/').map(|pos| Self(self.0[..pos].to_string()))
    }

    /// Get the depth in the hierarchy (0 = root)
    pub fn depth(&self) -> usize {
        if self.0.is_empty() { 0 } else { self.0.matches('/').count() }
    }

    /// Check if this is a child of another actor
    pub fn is_child_of(&self, parent: &ActorId) -> bool {
        self.0.starts_with(parent.as_str())
            && self.0.len() > parent.0.len()
            && self.0.chars().nth(parent.0.len()) == Some('/')
    }
}

impl From<&str> for ActorId {
    fn from(s: &str) -> Self {
        Self(s.to_string())
    }
}

impl From<String> for ActorId {
    fn from(s: String) -> Self {
        Self(s)
    }
}

/// Memory values are the fundamental data types in TAMTIL.
#[derive(Debug, Clone, PartialEq, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum MemoryValue {
    String(String),
    Number(f64),
    Boolean(bool),
    Bytes(Vec<u8>),
}

impl MemoryValue {
    pub fn string(s: impl Into<String>) -> Self { Self::String(s.into()) }
    pub fn number(n: f64) -> Self { Self::Number(n) }
    pub fn boolean(b: bool) -> Self { Self::Boolean(b) }
    pub fn bytes(b: Vec<u8>) -> Self { Self::Bytes(b) }
}

impl fmt::Display for MemoryValue {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Self::String(s) => write!(f, "{}", s),
            Self::Number(n) => write!(f, "{}", n),
            Self::Boolean(b) => write!(f, "{}", b),
            Self::Bytes(b) => write!(f, "{} bytes", b.len()),
        }
    }
}

/// Memory operations represent state changes that can be persisted.
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum MemoryOperation {
    Create { key: String, value: MemoryValue },
    Update { key: String, value: MemoryValue },
    Delete { key: String },
    Link { from: String, to: String, relation: String },
    Unlink { from: String, to: String, relation: String },
}

/// The Reaction trait is central to TAMTIL's design.
/// Every actor action produces a reaction that can be remembered.
pub trait Reaction: Archive + Send + Sync + 'static {
    /// Convert this reaction into memory operations for persistence
    fn remember(&self) -> Vec<MemoryOperation>;
}

// Example counter actor for testing and demonstration
pub mod examples {
    use super::*;
    use async_trait::async_trait;
    use rkyv::{api::high::to_bytes, rancor::Error as RancorError};

    /// Example counter action
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub enum CounterAction {
        Increment,
        Decrement,
        GetValue,
        SetValue(i32),
    }

    /// Example counter reaction
    #[derive(Debug, Clone, Default, Archive, Serialize, Deserialize)]
    #[rkyv(compare(PartialEq), derive(Debug))]
    pub struct CounterReaction {
        pub old_value: i32,
        pub new_value: i32,
        pub operation: String,
    }

    impl Reaction for CounterReaction {
        fn remember(&self) -> Vec<MemoryOperation> {
            vec![
                MemoryOperation::Update {
                    key: "counter_value".to_string(),
                    value: MemoryValue::number(self.new_value as f64),
                },
                MemoryOperation::Create {
                    key: format!("counter_operation_{}", std::time::SystemTime::now()
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap()
                        .as_secs()),
                    value: MemoryValue::string(&self.operation),
                }
            ]
        }
    }

    /// Example counter actor
    pub struct CounterActor {
        value: i32,
    }

    impl CounterActor {
        pub fn new(initial_value: i32) -> Self {
            Self { value: initial_value }
        }
    }

    #[async_trait]
    impl Actor for CounterActor {
        type Action = CounterAction;
        type Reaction = CounterReaction;

        async fn act(&mut self, action: Self::Action, _actors: &Actors, _memories: &Memories) -> TamtilResult<Vec<u8>> {
            let old_value = self.value;

            let (new_value, operation) = match action {
                CounterAction::Increment => {
                    self.value += 1;
                    (self.value, "increment".to_string())
                }
                CounterAction::Decrement => {
                    self.value -= 1;
                    (self.value, "decrement".to_string())
                }
                CounterAction::GetValue => {
                    (self.value, "get_value".to_string())
                }
                CounterAction::SetValue(val) => {
                    self.value = val;
                    (self.value, format!("set_value({})", val))
                }
            };

            let reaction = CounterReaction {
                old_value,
                new_value,
                operation,
            };

            // Serialize the reaction using rkyv
            let reaction_bytes = to_bytes::<RancorError>(&reaction)
                .map_err(|e| TamtilError::Serialization {
                    message: format!("Failed to serialize reaction: {}", e)
                })?;

            Ok(reaction_bytes.to_vec())
        }
    }
}




#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_actor_id_hierarchy() {
        let root = ActorId::new("platform.local");
        let child = root.child("web");
        let grandchild = child.child("session");

        assert_eq!(root.as_str(), "platform.local");
        assert_eq!(child.as_str(), "platform.local/web");
        assert_eq!(grandchild.as_str(), "platform.local/web/session");

        assert_eq!(root.depth(), 0);
        assert_eq!(child.depth(), 1);
        assert_eq!(grandchild.depth(), 2);

        assert!(child.is_child_of(&root));
        assert!(grandchild.is_child_of(&child));
        assert!(grandchild.is_child_of(&root));
        assert!(!root.is_child_of(&child));
    }

    #[test]
    fn test_memory_value_serialization() {
        use rkyv::{api::high::{to_bytes, from_bytes}, rancor::Error as RancorError};

        let value = MemoryValue::string("test");
        let bytes = to_bytes::<RancorError>(&value).expect("Failed to serialize");
        let deserialized: MemoryValue = from_bytes::<MemoryValue, RancorError>(&bytes).expect("Failed to deserialize");
        assert_eq!(value, deserialized);
    }

    #[test]
    fn test_memory_operation_serialization() {
        use rkyv::{api::high::{to_bytes, from_bytes}, rancor::Error as RancorError};

        let op = MemoryOperation::Create {
            key: "test_key".to_string(),
            value: MemoryValue::number(42.0),
        };
        let bytes = to_bytes::<RancorError>(&op).expect("Failed to serialize");
        let deserialized: MemoryOperation = from_bytes::<MemoryOperation, RancorError>(&bytes).expect("Failed to deserialize");

        match (&op, &deserialized) {
            (MemoryOperation::Create { key: k1, value: v1 }, MemoryOperation::Create { key: k2, value: v2 }) => {
                assert_eq!(k1, k2);
                assert_eq!(v1, v2);
            }
            _ => panic!("Deserialization changed operation type"),
        }
    }
}
