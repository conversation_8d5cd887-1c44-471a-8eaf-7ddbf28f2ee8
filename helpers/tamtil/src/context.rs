//! Context module - Service-level concerns and visual foundation
//! 
//! Context actors handle service-level concerns including visual foundation setup.
//! They provide the foundational visual infrastructure and manage service-specific
//! actor hierarchies within the platform.

use crate::core::{Tam<PERSON>Error, TamtilResult, ActorId, Action, Reaction, MemoryOperation, MemoryValue};
use crate::{Actor, Actors, Memories};
use async_trait::async_trait;
use rkyv::{Archive, api::high::to_bytes, rancor::Error as RancorError};
use std::fmt;

/// Context actions for managing service-level concerns
#[derive(Debug, Clone, Archive, rkyv::Serialize, rkyv::Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum ContextAction {
    /// Start the context and initialize visual foundation
    Start,
    /// Stop the context and cleanup resources
    Stop,
    /// Setup visual foundation (htmx, tailwind, etc.)
    SetupVisualFoundation,
    /// Create a child actor within this context
    SpawnChildActor { actor_type: String, actor_id: String },
    /// Get context status
    GetStatus,
}

#[async_trait]
impl Action for ContextAction {
    type Reaction = ContextReaction;

    fn act(self, _actors: &Actors, _memories: &Memories) -> impl std::future::Future<Output = TamtilResult<Self::Reaction>> + Send {
        async move {
        // Get context state from memories instead of actor
        let context_id = "default_context"; // In a real implementation, this would come from the action

        let operation = format!("{:?}", self);

        let reaction = match self {
            ContextAction::Start => {
                ContextReaction {
                    context_id: context_id.to_string(),
                    operation,
                    status: "started".to_string(),
                    message: "Context started successfully".to_string(),
                    visual_foundation_ready: true,
                }
            }
            ContextAction::Stop => {
                ContextReaction {
                    context_id: context_id.to_string(),
                    operation,
                    status: "stopped".to_string(),
                    message: "Context stopped successfully".to_string(),
                    visual_foundation_ready: false,
                }
            }
            ContextAction::SetupVisualFoundation => {
                ContextReaction {
                    context_id: context_id.to_string(),
                    operation,
                    status: "running".to_string(),
                    message: "Visual foundation setup complete".to_string(),
                    visual_foundation_ready: true,
                }
            }
            ContextAction::SpawnChildActor { actor_type, actor_id } => {
                tracing::info!("Would spawn {} actor with id {}", actor_type, actor_id);

                ContextReaction {
                    context_id: context_id.to_string(),
                    operation,
                    status: "running".to_string(),
                    message: format!("Child actor {} spawned", actor_id),
                    visual_foundation_ready: true,
                }
            }
            ContextAction::GetStatus => {
                ContextReaction {
                    context_id: context_id.to_string(),
                    operation,
                    status: "running".to_string(),
                    message: "Context status retrieved".to_string(),
                    visual_foundation_ready: true,
                }
            }
        };

        Ok(reaction)
        }
    }
}

/// Context reactions for service-level responses
#[derive(Debug, Clone, Default, Archive, rkyv::Serialize, rkyv::Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub struct ContextReaction {
    pub context_id: String,
    pub operation: String,
    pub status: String,
    pub message: String,
    pub visual_foundation_ready: bool,
}

impl Reaction for ContextReaction {
    fn remember(&self) -> Vec<MemoryOperation> {
        vec![
            MemoryOperation::Update {
                key: format!("context:{}", self.context_id),
                value: MemoryValue::string(&self.status).unwrap_or_else(|_| MemoryValue::from_bytes(vec![])),
            },
            MemoryOperation::Create {
                key: format!("context:{}:operation", self.context_id),
                value: MemoryValue::string(&self.operation).unwrap_or_else(|_| MemoryValue::from_bytes(vec![])),
            },
            MemoryOperation::Update {
                key: format!("context:{}:visual_ready", self.context_id),
                value: MemoryValue::boolean(self.visual_foundation_ready).unwrap_or_else(|_| MemoryValue::from_bytes(vec![])),
            }
        ]
    }
}

/// Context actor that manages service-level concerns
pub struct ContextActor {
    pub id: String,
    pub status: String,
    pub visual_foundation_ready: bool,
    pub child_actors: Vec<String>,
}

impl ContextActor {
    pub fn new(id: String) -> Self {
        Self {
            id,
            status: "created".to_string(),
            visual_foundation_ready: false,
            child_actors: Vec::new(),
        }
    }

    /// Setup visual foundation with htmx, tailwind, and _hyperscript
    pub async fn setup_visual_foundation(&mut self) -> TamtilResult<()> {
        // In a full implementation, this would:
        // 1. Initialize htmx endpoints
        // 2. Setup tailwind CSS compilation
        // 3. Configure _hyperscript behaviors
        // 4. Create base HTML templates
        
        tracing::info!("Setting up visual foundation for context {}", self.id);
        self.visual_foundation_ready = true;
        Ok(())
    }

    /// Start the context and all its services
    pub async fn start(&mut self, actors: &Actors, memories: &Memories) -> TamtilResult<()> {
        tracing::info!("Starting context {}", self.id);
        
        // Setup visual foundation first
        self.setup_visual_foundation().await?;
        
        // Initialize context state in memories
        let operations = vec![
            MemoryOperation::Create {
                key: format!("context:{}", self.id),
                value: MemoryValue::string("started")?,
            },
            MemoryOperation::Create {
                key: format!("context:{}:visual_ready", self.id),
                value: MemoryValue::boolean(true)?,
            }
        ];
        
        memories.remember(operations).await?;
        self.status = "started".to_string();
        
        Ok(())
    }

    /// Stop the context and cleanup
    pub async fn stop(&mut self, actors: &Actors, memories: &Memories) -> TamtilResult<()> {
        tracing::info!("Stopping context {}", self.id);
        
        // In a full implementation, this would:
        // 1. Stop all child actors
        // 2. Cleanup visual resources
        // 3. Close htmx endpoints
        
        let operations = vec![
            MemoryOperation::Update {
                key: format!("context:{}", self.id),
                value: MemoryValue::string("stopped")?,
            }
        ];
        
        memories.remember(operations).await?;
        self.status = "stopped".to_string();
        self.visual_foundation_ready = false;
        
        Ok(())
    }
}

impl Actor for ContextActor {
    type Action = ContextAction;
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::actor::ActorHandle;

    #[tokio::test]
    async fn test_context_lifecycle() {
        let memories = Memories::new("test").await.unwrap();
        let actors = Actors::new(memories.clone());
        
        let mut context = ContextActor::new("web".to_string());
        
        // Test start
        let reaction_bytes = context.act(ContextAction::Start, &actors, &memories).await.unwrap();
        let reaction = ActorHandle::<ContextActor>::access_reaction::<ContextReaction>(&reaction_bytes).unwrap();
        
        assert_eq!(reaction.status.as_str(), "started");
        assert!(reaction.visual_foundation_ready);
        
        // Test status
        let reaction_bytes = context.act(ContextAction::GetStatus, &actors, &memories).await.unwrap();
        let reaction = ActorHandle::<ContextActor>::access_reaction::<ContextReaction>(&reaction_bytes).unwrap();
        
        assert_eq!(reaction.status.as_str(), "started");
        
        // Test stop
        let reaction_bytes = context.act(ContextAction::Stop, &actors, &memories).await.unwrap();
        let reaction = ActorHandle::<ContextActor>::access_reaction::<ContextReaction>(&reaction_bytes).unwrap();
        
        assert_eq!(reaction.status.as_str(), "stopped");
        assert!(!reaction.visual_foundation_ready);
    }
}
