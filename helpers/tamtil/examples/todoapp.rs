//! TodoApp CLI Example - Clean TAMTIL Implementation
//!
//! This example demonstrates proper TAMTIL patterns:
//! - Actions contain the business logic in their act() method
//! - Reactions contain state changes in their remember() method
//! - Actors are immutable - state only changed via memories.remember()
//! - State only accessed via memories.recall()
//! - Zero-copy serialization with rkyv

use tamtil::{
    Actor, Actors, Memories, Action, Reaction, TamtilResult, TamtilError,
    MemoryOperation, MemoryValue, Actor<PERSON><PERSON>,
    actor::<PERSON><PERSON><PERSON><PERSON>,
};
use async_trait::async_trait;
use rkyv::{Archive, api::high::to_bytes, rancor::Error as RancorError};
use tokio;
use clap::{Parser, Subcommand};

/// Todo item structure
#[derive(Debug, Clone, Archive, rkyv::Serialize, rkyv::Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub struct TodoItem {
    pub id: u32,
    pub title: String,
    pub description: String,
    pub completed: bool,
    pub created_at: u64,
}

/// Todo actions - contain the business logic
#[derive(Debug, Clone, Archive, rkyv::Serialize, rkyv::Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum TodoAction {
    Add { title: String, description: String },
    Complete { id: u32 },
    Delete { id: u32 },
    List,
    Get { id: u32 },
}

/// Todo reactions - contain the results and state changes
#[derive(Debug, Clone, Default, Archive, rkyv::Serialize, rkyv::Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub struct TodoReaction {
    pub success: bool,
    pub message: String,
    pub todo_item: Option<TodoItem>,
    pub todo_list: Vec<TodoItem>,
}

/// Action implementations - business logic using only memories.recall() and memories.remember()
#[async_trait]
impl Action for TodoAction {
    type Reaction = TodoReaction;

    fn act(self, _actors: &Actors, memories: &Memories) -> impl std::future::Future<Output = TamtilResult<Self::Reaction>> + Send {
        async move {
        // All state access must go through memories.recall()
        // All state changes must go through the reaction's remember() method

        match self {
            TodoAction::Add { title, description } => {
                // Get the next ID from memory using generic recall
                let next_id: u32 = memories.recall::<u32>("todoapp:next_id").await.unwrap_or(1);

                let todo = TodoItem {
                    id: next_id,
                    title: title.clone(),
                    description: description.clone(),
                    completed: false,
                    created_at: std::time::SystemTime::now()
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap()
                        .as_secs(),
                };

                Ok(TodoReaction {
                    success: true,
                    message: format!("Added todo: {}", title),
                    todo_item: Some(todo),
                    todo_list: vec![],
                })
            }
            TodoAction::Complete { id } => {
                // Get todo using generic recall with zero-copy
                if let Some(mut todo) = memories.recall::<TodoItem>(&format!("todos:item_{}", id)).await {
                    todo.completed = true;
                    Ok(TodoReaction {
                        success: true,
                        message: format!("Completed todo: {}", todo.title),
                        todo_item: Some(todo),
                        todo_list: vec![],
                    })
                } else {
                    Ok(TodoReaction {
                        success: false,
                        message: format!("Todo with id {} not found", id),
                        todo_item: None,
                        todo_list: vec![],
                    })
                }
            }
            TodoAction::Delete { id } => {
                // Get todo using generic recall
                if let Some(todo) = memories.recall::<TodoItem>(&format!("todos:item_{}", id)).await {
                    Ok(TodoReaction {
                        success: true,
                        message: format!("Deleted todo: {}", todo.title),
                        todo_item: Some(todo),
                        todo_list: vec![],
                    })
                } else {
                    Ok(TodoReaction {
                        success: false,
                        message: format!("Todo with id {} not found", id),
                        todo_item: None,
                        todo_list: vec![],
                    })
                }
            }
            TodoAction::List => {
                // Get all todo keys and recall each one using generic recall
                let keys = memories.keys("todos").await;
                let mut todos = Vec::new();

                for key in keys {
                    if key.starts_with("item_") {
                        if let Some(todo) = memories.recall::<TodoItem>(&format!("todos:{}", key)).await {
                            todos.push(todo);
                        }
                    }
                }

                todos.sort_by_key(|t| t.id);
                Ok(TodoReaction {
                    success: true,
                    message: format!("Found {} todos", todos.len()),
                    todo_item: None,
                    todo_list: todos,
                })
            }
            TodoAction::Get { id } => {
                // Get todo using generic recall
                if let Some(todo) = memories.recall::<TodoItem>(&format!("todos:item_{}", id)).await {
                    Ok(TodoReaction {
                        success: true,
                        message: format!("Found todo: {}", todo.title),
                        todo_item: Some(todo),
                        todo_list: vec![],
                    })
                } else {
                    Ok(TodoReaction {
                        success: false,
                        message: format!("Todo with id {} not found", id),
                        todo_item: None,
                        todo_list: vec![],
                    })
                }
            }
        }
        }
    }
}

/// Reaction implementation - this is where state changes are persisted
impl Reaction for TodoReaction {
    fn remember(&self) -> Vec<MemoryOperation> {
        let mut operations = vec![];

        // Always log the operation
        if let Ok(message_value) = MemoryValue::new(&self.message) {
            operations.push(MemoryOperation::Create {
                key: format!("todo_operation_{}", std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap()
                    .as_millis()),
                value: message_value,
            });
        }

        // Handle state changes based on the reaction using rkyv zero-copy serialization
        if let Some(ref todo) = self.todo_item {
            // Serialize todo using rkyv for zero-copy access
            if let Ok(todo_memory_value) = MemoryValue::new(todo) {
                // For Add operations, store in todos memory and update next_id
                if self.message.starts_with("Added todo:") {
                    operations.push(MemoryOperation::Create {
                        key: format!("todos:item_{}", todo.id),
                        value: todo_memory_value,
                    });

                    // Update next_id in todoapp memory
                    if let Ok(next_id_value) = MemoryValue::new(&(todo.id + 1)) {
                        operations.push(MemoryOperation::Update {
                            key: "todoapp:next_id".to_string(),
                            value: next_id_value,
                        });
                    }
                }
                // For Complete operations, update the existing todo in todos memory
                else if self.message.starts_with("Completed todo:") {
                    operations.push(MemoryOperation::Update {
                        key: format!("todos:item_{}", todo.id),
                        value: todo_memory_value,
                    });
                }
                // For Delete operations, remove the todo from todos memory
                else if self.message.starts_with("Deleted todo:") {
                    operations.push(MemoryOperation::Delete {
                        key: format!("todos:item_{}", todo.id),
                    });
                }
                // For other operations (Get), no state change needed
            }
        }

        // Update todo count if we have a list (for List operations)
        if !self.todo_list.is_empty() {
            if let Ok(count_value) = MemoryValue::new(&self.todo_list.len()) {
                operations.push(MemoryOperation::Update {
                    key: "todo_count".to_string(),
                    value: count_value,
                });
            }
        }

        operations
    }
}

/// Todo actor - immutable actor, all state is in memories
pub struct TodoActor {
    // No state - everything is in memories
}

impl TodoActor {
    pub fn new() -> Self {
        Self {}
    }
}

impl Actor for TodoActor {
    type Action = TodoAction;
}

/// CLI interface
#[derive(Parser)]
#[command(name = "todoapp")]
#[command(about = "A todo list application built with TAMTIL")]
struct Cli {
    #[command(subcommand)]
    command: Commands,
}

#[derive(Subcommand)]
enum Commands {
    /// Add a new todo item
    Add {
        title: String,
        #[arg(short, long)]
        description: Option<String>,
    },
    /// Mark a todo as completed
    Complete { id: u32 },
    /// Delete a todo item
    Delete { id: u32 },
    /// List all todos
    List,
    /// Get a specific todo by ID
    Get { id: u32 },
}

#[tokio::main]
async fn main() -> TamtilResult<()> {
    // Initialize tracing
    tracing_subscriber::fmt::init();

    let cli = Cli::parse();

    // Initialize TAMTIL system
    let memories = Memories::new().await?;
    let actors = Actors::new(memories.clone());

    // Spawn the todo actor
    let todo_actor = TodoActor::new();
    let todo_handle = actors.spawn(
        ActorId::new("todoapp/todo_manager"),
        todo_actor,
    ).await?;

    // Convert CLI command to TodoAction
    let action = match cli.command {
        Commands::Add { title, description } => {
            TodoAction::Add {
                title,
                description: description.unwrap_or_default(),
            }
        }
        Commands::Complete { id } => TodoAction::Complete { id },
        Commands::Delete { id } => TodoAction::Delete { id },
        Commands::List => TodoAction::List,
        Commands::Get { id } => TodoAction::Get { id },
    };

    // Send action to todo actor and get reaction
    let reaction_bytes = todo_handle.ask(action).await?;
    let reaction = ActorHandle::<TodoActor>::access_reaction::<TodoReaction>(&reaction_bytes)?;

    // Display results
    if reaction.success {
        println!("✅ {}", reaction.message.as_str());
        
        if let rkyv::option::ArchivedOption::Some(ref todo) = reaction.todo_item {
            println!("   ID: {}", todo.id);
            println!("   Title: {}", todo.title.as_str());
            println!("   Description: {}", todo.description.as_str());
            println!("   Completed: {}", if todo.completed { "✅" } else { "❌" });
        }
        
        if !reaction.todo_list.is_empty() {
            println!("\n📋 Todo List:");
            for todo in reaction.todo_list.iter() {
                let status = if todo.completed { "✅" } else { "❌" };
                println!("   {} [{}] {} - {}", status, todo.id, todo.title.as_str(), todo.description.as_str());
            }
        }
    } else {
        println!("❌ {}", reaction.message.as_str());
    }

    // Shutdown the actor system
    actors.shutdown_all().await?;

    Ok(())
}
