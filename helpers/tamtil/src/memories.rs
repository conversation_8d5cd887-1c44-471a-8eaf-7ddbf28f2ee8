//! Memories module - Graph-based in-memory rkyv store
//!
//! Memories are collections of key-values under one ID. These collections can be
//! connected and disconnected and queried like a graph. All data is stored using
//! rkyv for zero-copy access.

use crate::core::{TamtilResult, MemoryValue, MemoryOperation};
use std::{collections::HashMap, sync::Arc};
use tokio::sync::RwLock;
use rkyv::{Archive, api::high::{to_bytes, from_bytes}, rancor::Error as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>};

/// A Memory is a collection of key-values under one ID
#[derive(Debug, Clone, Archive, rkyv::Serialize, rkyv::Deserialize)]
#[rkyv(derive(Debug))]
pub struct Memory {
    pub id: String,
    pub data: HashMap<String, MemoryValue>,
    pub connections: Vec<String>, // IDs of connected memories
}

impl Memory {
    pub fn new(id: String) -> Self {
        Self {
            id,
            data: HashMap::new(),
            connections: Vec::new(),
        }
    }

    pub fn get(&self, key: &str) -> Option<&MemoryValue> {
        self.data.get(key)
    }

    pub fn set(&mut self, key: String, value: MemoryValue) {
        self.data.insert(key, value);
    }

    pub fn remove(&mut self, key: &str) -> Option<MemoryValue> {
        self.data.remove(key)
    }

    pub fn connect(&mut self, memory_id: String) {
        if !self.connections.contains(&memory_id) {
            self.connections.push(memory_id);
        }
    }

    pub fn disconnect(&mut self, memory_id: &str) {
        self.connections.retain(|id| id != memory_id);
    }
}

/// Graph-based memories system with in-memory rkyv storage
#[derive(Clone)]
pub struct Memories {
    memories: Arc<RwLock<HashMap<String, Vec<u8>>>>, // memory_id -> serialized Memory
}

impl Memories {
    /// Create a new graph-based memories system (in-memory only)
    pub async fn new() -> TamtilResult<Self> {
        Ok(Self {
            memories: Arc::new(RwLock::new(HashMap::new())),
        })
    }

    /// Get or create a memory by ID
    async fn get_or_create_memory(&self, memory_id: &str) -> TamtilResult<Memory> {
        let memories = self.memories.read().await;

        if let Some(memory_bytes) = memories.get(memory_id) {
            // Deserialize existing memory
            from_bytes::<Memory, RancorError>(memory_bytes)
                .map_err(|e| crate::core::TamtilError::Deserialization {
                    message: format!("Failed to deserialize memory {}: {}", memory_id, e)
                })
        } else {
            // Create new memory
            Ok(Memory::new(memory_id.to_string()))
        }
    }

    /// Save a memory back to storage
    async fn save_memory(&self, memory: &Memory) -> TamtilResult<()> {
        let memory_bytes = to_bytes::<RancorError>(memory)
            .map_err(|e| crate::core::TamtilError::Serialization {
                message: format!("Failed to serialize memory {}: {}", memory.id, e)
            })?;

        let mut memories = self.memories.write().await;
        memories.insert(memory.id.clone(), memory_bytes.to_vec());
        Ok(())
    }

    /// Remember (persist) memory operations atomically - all succeed or all fail
    pub async fn remember(&self, operations: Vec<MemoryOperation>) -> TamtilResult<()> {
        if operations.is_empty() {
            return Ok(());
        }

        // Collect all affected memories first to validate operations
        let mut affected_memories = HashMap::new();

        // Pre-validate all operations and collect affected memories
        for operation in &operations {
            match operation {
                MemoryOperation::Create { key, .. } |
                MemoryOperation::Update { key, .. } |
                MemoryOperation::Delete { key } => {
                    let (memory_id, _) = if let Some(pos) = key.find(':') {
                        (&key[..pos], &key[pos + 1..])
                    } else {
                        ("default", key.as_str())
                    };

                    if !affected_memories.contains_key(memory_id) {
                        let memory = self.get_or_create_memory(memory_id).await?;
                        affected_memories.insert(memory_id.to_string(), memory);
                    }
                }
                MemoryOperation::Link { from, to, .. } |
                MemoryOperation::Unlink { from, to, .. } => {
                    if !affected_memories.contains_key(from) {
                        let memory = self.get_or_create_memory(from).await?;
                        affected_memories.insert(from.clone(), memory);
                    }
                    if !affected_memories.contains_key(to) {
                        let memory = self.get_or_create_memory(to).await?;
                        affected_memories.insert(to.clone(), memory);
                    }
                }
            }
        }

        // Apply all operations to the in-memory copies
        for operation in operations {
            match operation {
                MemoryOperation::Create { key, value } | MemoryOperation::Update { key, value } => {
                    let (memory_id, memory_key) = if let Some(pos) = key.find(':') {
                        (&key[..pos], &key[pos + 1..])
                    } else {
                        ("default", key.as_str())
                    };

                    if let Some(memory) = affected_memories.get_mut(memory_id) {
                        memory.set(memory_key.to_string(), value);
                        tracing::debug!("Applied {}:{}", memory_id, memory_key);
                    }
                }
                MemoryOperation::Delete { key } => {
                    let (memory_id, memory_key) = if let Some(pos) = key.find(':') {
                        (&key[..pos], &key[pos + 1..])
                    } else {
                        ("default", key.as_str())
                    };

                    if let Some(memory) = affected_memories.get_mut(memory_id) {
                        memory.remove(memory_key);
                        tracing::debug!("Deleted {}:{}", memory_id, memory_key);
                    }
                }
                MemoryOperation::Link { from, to, relation: _ } => {
                    if let Some(from_memory) = affected_memories.get_mut(&from) {
                        from_memory.connect(to.clone());
                        tracing::debug!("Connected {} -> {}", from, to);
                    }
                }
                MemoryOperation::Unlink { from, to, relation: _ } => {
                    if let Some(from_memory) = affected_memories.get_mut(&from) {
                        from_memory.disconnect(&to);
                        tracing::debug!("Disconnected {} -> {}", from, to);
                    }
                }
            }
        }

        // Atomically save all affected memories
        for (_, memory) in affected_memories {
            self.save_memory(&memory).await?;
        }

        Ok(())
    }

    /// Recall (retrieve) a memory value
    pub async fn recall(&self, key: &str) -> Option<MemoryValue> {
        let (memory_id, memory_key) = if let Some(pos) = key.find(':') {
            (&key[..pos], &key[pos + 1..])
        } else {
            ("default", key)
        };

        if let Ok(memory) = self.get_or_create_memory(memory_id).await {
            memory.get(memory_key).cloned()
        } else {
            None
        }
    }

    /// Get a complete memory by ID
    pub async fn get_memory(&self, memory_id: &str) -> Option<Memory> {
        self.get_or_create_memory(memory_id).await.ok()
    }

    /// List all memory IDs
    pub async fn memory_ids(&self) -> Vec<String> {
        let memories = self.memories.read().await;
        memories.keys().cloned().collect()
    }

    /// List all keys in a specific memory
    pub async fn keys(&self, memory_id: &str) -> Vec<String> {
        if let Ok(memory) = self.get_or_create_memory(memory_id).await {
            memory.data.keys().cloned().collect()
        } else {
            Vec::new()
        }
    }

    /// Get connected memories (graph traversal)
    pub async fn get_connected(&self, memory_id: &str) -> Vec<String> {
        if let Ok(memory) = self.get_or_create_memory(memory_id).await {
            memory.connections.clone()
        } else {
            Vec::new()
        }
    }

    /// Query the graph - find all memories connected to a given memory
    pub async fn query_graph(&self, start_memory_id: &str, max_depth: usize) -> Vec<String> {
        let mut visited = std::collections::HashSet::new();
        let mut to_visit = vec![(start_memory_id.to_string(), 0)];
        let mut result = Vec::new();

        while let Some((memory_id, depth)) = to_visit.pop() {
            if visited.contains(&memory_id) || depth > max_depth {
                continue;
            }

            visited.insert(memory_id.clone());
            result.push(memory_id.clone());

            if depth < max_depth {
                let connected = self.get_connected(&memory_id).await;
                for connected_id in connected {
                    if !visited.contains(&connected_id) {
                        to_visit.push((connected_id, depth + 1));
                    }
                }
            }
        }

        result
    }

    /// Clear all memories (useful for testing)
    pub async fn clear(&self) -> TamtilResult<()> {
        let mut memories = self.memories.write().await;
        memories.clear();
        tracing::debug!("Cleared all memories");
        Ok(())
    }

    /// Get the total number of memories
    pub async fn len(&self) -> usize {
        let memories = self.memories.read().await;
        memories.len()
    }

    /// Check if the memory store is empty
    pub async fn is_empty(&self) -> bool {
        let memories = self.memories.read().await;
        memories.is_empty()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    /// Test basic memory operations as a blackbox
    #[tokio::test]
    async fn test_basic_memory_operations() {
        let memories = Memories::new().await.unwrap();

        // Test Create operation with both simple values and rkyv serialized data
        #[derive(Debug, Clone, Archive, rkyv::Serialize, rkyv::Deserialize)]
        #[rkyv(derive(Debug))]
        struct User {
            name: String,
            age: u32,
            active: bool,
        }

        let user = User {
            name: "Alice".to_string(),
            age: 30,
            active: true,
        };

        let operations = vec![
            MemoryOperation::Create {
                key: "user:123:name".to_string(),
                value: MemoryValue::string("Alice"),
            },
            MemoryOperation::Create {
                key: "user:123:age".to_string(),
                value: MemoryValue::number(30.0),
            },
            MemoryOperation::Create {
                key: "user:123:profile".to_string(),
                value: MemoryValue::from_rkyv(&user).unwrap(),
            }
        ];

        memories.remember(operations).await.unwrap();

        // Test Recall
        let name = memories.recall("user:123:name").await;
        assert!(matches!(name, Some(MemoryValue::String(ref s)) if s == "Alice"));

        let age = memories.recall("user:123:age").await;
        assert!(matches!(age, Some(MemoryValue::Number(n)) if n == 30.0));
    }

    /// Test atomic operations - all succeed or all fail
    #[tokio::test]
    async fn test_atomic_operations() {
        let memories = Memories::new().await.unwrap();

        // First, create some initial data
        let setup_ops = vec![
            MemoryOperation::Create {
                key: "account:1:balance".to_string(),
                value: MemoryValue::number(100.0),
            }
        ];
        memories.remember(setup_ops).await.unwrap();

        // Test atomic transaction
        let transaction_ops = vec![
            MemoryOperation::Update {
                key: "account:1:balance".to_string(),
                value: MemoryValue::number(50.0),
            },
            MemoryOperation::Create {
                key: "transaction:tx1:amount".to_string(),
                value: MemoryValue::number(50.0),
            },
            MemoryOperation::Create {
                key: "transaction:tx1:from".to_string(),
                value: MemoryValue::string("account:1"),
            }
        ];

        memories.remember(transaction_ops).await.unwrap();

        // Verify all operations succeeded
        let balance = memories.recall("account:1:balance").await;
        assert!(matches!(balance, Some(MemoryValue::Number(n)) if n == 50.0));

        let amount = memories.recall("transaction:tx1:amount").await;
        assert!(matches!(amount, Some(MemoryValue::Number(n)) if n == 50.0));

        let from = memories.recall("transaction:tx1:from").await;
        assert!(matches!(from, Some(MemoryValue::String(ref s)) if s == "account:1"));
    }

    /// Test graph connections and traversal
    #[tokio::test]
    async fn test_graph_operations() {
        let memories = Memories::new().await.unwrap();

        // Create a graph: user -> posts -> comments
        let graph_ops = vec![
            // Create user memory
            MemoryOperation::Create {
                key: "users:alice:name".to_string(),
                value: MemoryValue::string("Alice"),
            },
            // Create post memory
            MemoryOperation::Create {
                key: "posts:post1:title".to_string(),
                value: MemoryValue::string("Hello World"),
            },
            // Create comment memory
            MemoryOperation::Create {
                key: "comments:c1:text".to_string(),
                value: MemoryValue::string("Great post!"),
            },
            // Link user to post
            MemoryOperation::Link {
                from: "users".to_string(),
                to: "posts".to_string(),
                relation: "authored".to_string(),
            },
            // Link post to comment
            MemoryOperation::Link {
                from: "posts".to_string(),
                to: "comments".to_string(),
                relation: "has_comment".to_string(),
            }
        ];

        memories.remember(graph_ops).await.unwrap();

        // Test graph traversal
        let connected_to_users = memories.get_connected("users").await;
        assert!(connected_to_users.contains(&"posts".to_string()));

        let connected_to_posts = memories.get_connected("posts").await;
        assert!(connected_to_posts.contains(&"comments".to_string()));

        // Test graph query with depth
        let graph_result = memories.query_graph("users", 2).await;
        assert!(graph_result.contains(&"users".to_string()));
        assert!(graph_result.contains(&"posts".to_string()));
        assert!(graph_result.contains(&"comments".to_string()));
    }

    /// Test memory deletion and cleanup
    #[tokio::test]
    async fn test_memory_deletion() {
        let memories = Memories::new().await.unwrap();

        // Create some data
        let create_ops = vec![
            MemoryOperation::Create {
                key: "temp:data:value".to_string(),
                value: MemoryValue::string("temporary"),
            },
            MemoryOperation::Create {
                key: "temp:data:count".to_string(),
                value: MemoryValue::number(42.0),
            }
        ];
        memories.remember(create_ops).await.unwrap();

        // Verify data exists
        assert!(memories.recall("temp:data:value").await.is_some());
        assert!(memories.recall("temp:data:count").await.is_some());

        // Delete data
        let delete_ops = vec![
            MemoryOperation::Delete {
                key: "temp:data:value".to_string(),
            },
            MemoryOperation::Delete {
                key: "temp:data:count".to_string(),
            }
        ];
        memories.remember(delete_ops).await.unwrap();

        // Verify data is gone
        assert!(memories.recall("temp:data:value").await.is_none());
        assert!(memories.recall("temp:data:count").await.is_none());
    }

    /// Test event sourcing pattern - reactions to operations to projections
    #[tokio::test]
    async fn test_event_sourcing_pattern() {
        let memories = Memories::new().await.unwrap();

        // Simulate a series of reactions creating operations

        // Reaction 1: User registration
        let user_registration_ops = vec![
            MemoryOperation::Create {
                key: "events:1:type".to_string(),
                value: MemoryValue::string("UserRegistered"),
            },
            MemoryOperation::Create {
                key: "events:1:user_id".to_string(),
                value: MemoryValue::string("user123"),
            },
            MemoryOperation::Create {
                key: "events:1:timestamp".to_string(),
                value: MemoryValue::number(1234567890.0),
            },
            // Projection: Create user
            MemoryOperation::Create {
                key: "users:user123:status".to_string(),
                value: MemoryValue::string("active"),
            }
        ];
        memories.remember(user_registration_ops).await.unwrap();

        // Reaction 2: User profile update
        let profile_update_ops = vec![
            MemoryOperation::Create {
                key: "events:2:type".to_string(),
                value: MemoryValue::string("ProfileUpdated"),
            },
            MemoryOperation::Create {
                key: "events:2:user_id".to_string(),
                value: MemoryValue::string("user123"),
            },
            MemoryOperation::Update {
                key: "users:user123:name".to_string(),
                value: MemoryValue::string("John Doe"),
            }
        ];
        memories.remember(profile_update_ops).await.unwrap();

        // Query the event log
        let event1_type = memories.recall("events:1:type").await;
        assert!(matches!(event1_type, Some(MemoryValue::String(ref s)) if s == "UserRegistered"));

        let event2_type = memories.recall("events:2:type").await;
        assert!(matches!(event2_type, Some(MemoryValue::String(ref s)) if s == "ProfileUpdated"));

        // Query the projection
        let user_status = memories.recall("users:user123:status").await;
        assert!(matches!(user_status, Some(MemoryValue::String(ref s)) if s == "active"));

        let user_name = memories.recall("users:user123:name").await;
        assert!(matches!(user_name, Some(MemoryValue::String(ref s)) if s == "John Doe"));
    }

    /// Test optimized graph queries for inspection
    #[tokio::test]
    async fn test_optimized_graph_queries() {
        let memories = Memories::new().await.unwrap();

        // Create a complex graph structure
        let setup_ops = vec![
            // Create multiple memories with connections
            MemoryOperation::Create {
                key: "orders:order1:total".to_string(),
                value: MemoryValue::number(100.0),
            },
            MemoryOperation::Create {
                key: "customers:cust1:name".to_string(),
                value: MemoryValue::string("Customer 1"),
            },
            MemoryOperation::Create {
                key: "products:prod1:name".to_string(),
                value: MemoryValue::string("Product 1"),
            },
            // Link them together
            MemoryOperation::Link {
                from: "customers".to_string(),
                to: "orders".to_string(),
                relation: "placed".to_string(),
            },
            MemoryOperation::Link {
                from: "orders".to_string(),
                to: "products".to_string(),
                relation: "contains".to_string(),
            }
        ];
        memories.remember(setup_ops).await.unwrap();

        // Test memory listing
        let memory_ids = memories.memory_ids().await;
        assert!(memory_ids.contains(&"orders".to_string()));
        assert!(memory_ids.contains(&"customers".to_string()));
        assert!(memory_ids.contains(&"products".to_string()));

        // Test key listing within a memory
        let order_keys = memories.keys("orders").await;
        assert!(order_keys.contains(&"order1:total".to_string()));

        // Test graph traversal with different depths
        let depth_1 = memories.query_graph("customers", 1).await;
        assert!(depth_1.contains(&"customers".to_string()));
        assert!(depth_1.contains(&"orders".to_string()));
        assert!(!depth_1.contains(&"products".to_string())); // Should not reach depth 2

        let depth_2 = memories.query_graph("customers", 2).await;
        assert!(depth_2.contains(&"customers".to_string()));
        assert!(depth_2.contains(&"orders".to_string()));
        assert!(depth_2.contains(&"products".to_string())); // Should reach depth 2
    }

    /// Test memory system performance and consistency
    #[tokio::test]
    async fn test_memory_consistency() {
        let memories = Memories::new().await.unwrap();

        // Test that empty operations don't cause issues
        memories.remember(vec![]).await.unwrap();

        // Test that non-existent recalls return None
        assert!(memories.recall("non:existent:key").await.is_none());

        // Test that memory IDs are properly isolated
        let ops = vec![
            MemoryOperation::Create {
                key: "mem1:key".to_string(),
                value: MemoryValue::string("value1"),
            },
            MemoryOperation::Create {
                key: "mem2:key".to_string(),
                value: MemoryValue::string("value2"),
            }
        ];
        memories.remember(ops).await.unwrap();

        let val1 = memories.recall("mem1:key").await;
        let val2 = memories.recall("mem2:key").await;

        assert!(matches!(val1, Some(MemoryValue::String(ref s)) if s == "value1"));
        assert!(matches!(val2, Some(MemoryValue::String(ref s)) if s == "value2"));

        // Test that keys within memories are properly isolated
        let mem1_keys = memories.keys("mem1").await;
        let mem2_keys = memories.keys("mem2").await;

        assert!(mem1_keys.contains(&"key".to_string()));
        assert!(mem2_keys.contains(&"key".to_string()));
        assert_eq!(mem1_keys.len(), 1);
        assert_eq!(mem2_keys.len(), 1);
    }

    /// Test production-ready scenarios with complex operations
    #[tokio::test]
    async fn test_production_scenarios() {
        let memories = Memories::new().await.unwrap();

        // Scenario: E-commerce order processing with event sourcing

        // Step 1: Customer places order (atomic operation)
        let order_placed_ops = vec![
            // Event log
            MemoryOperation::Create {
                key: "events:1:type".to_string(),
                value: MemoryValue::string("OrderPlaced"),
            },
            MemoryOperation::Create {
                key: "events:1:order_id".to_string(),
                value: MemoryValue::string("order_123"),
            },
            MemoryOperation::Create {
                key: "events:1:customer_id".to_string(),
                value: MemoryValue::string("customer_456"),
            },
            // Projections
            MemoryOperation::Create {
                key: "orders:order_123:status".to_string(),
                value: MemoryValue::string("placed"),
            },
            MemoryOperation::Create {
                key: "orders:order_123:total".to_string(),
                value: MemoryValue::number(99.99),
            },
            MemoryOperation::Update {
                key: "customers:customer_456:order_count".to_string(),
                value: MemoryValue::number(1.0),
            },
            // Graph connections
            MemoryOperation::Link {
                from: "customers".to_string(),
                to: "orders".to_string(),
                relation: "placed".to_string(),
            }
        ];
        memories.remember(order_placed_ops).await.unwrap();

        // Step 2: Payment processed (another atomic operation)
        let payment_processed_ops = vec![
            MemoryOperation::Create {
                key: "events:2:type".to_string(),
                value: MemoryValue::string("PaymentProcessed"),
            },
            MemoryOperation::Create {
                key: "events:2:order_id".to_string(),
                value: MemoryValue::string("order_123"),
            },
            MemoryOperation::Update {
                key: "orders:order_123:status".to_string(),
                value: MemoryValue::string("paid"),
            },
            MemoryOperation::Create {
                key: "payments:payment_789:amount".to_string(),
                value: MemoryValue::number(99.99),
            },
            MemoryOperation::Link {
                from: "orders".to_string(),
                to: "payments".to_string(),
                relation: "paid_with".to_string(),
            }
        ];
        memories.remember(payment_processed_ops).await.unwrap();

        // Verify the complete state

        // Check event log
        let event1 = memories.recall("events:1:type").await;
        assert!(matches!(event1, Some(MemoryValue::String(ref s)) if s == "OrderPlaced"));

        let event2 = memories.recall("events:2:type").await;
        assert!(matches!(event2, Some(MemoryValue::String(ref s)) if s == "PaymentProcessed"));

        // Check projections
        let order_status = memories.recall("orders:order_123:status").await;
        assert!(matches!(order_status, Some(MemoryValue::String(ref s)) if s == "paid"));

        let order_total = memories.recall("orders:order_123:total").await;
        assert!(matches!(order_total, Some(MemoryValue::Number(n)) if n == 99.99));

        let customer_orders = memories.recall("customers:customer_456:order_count").await;
        assert!(matches!(customer_orders, Some(MemoryValue::Number(n)) if n == 1.0));

        let payment_amount = memories.recall("payments:payment_789:amount").await;
        assert!(matches!(payment_amount, Some(MemoryValue::Number(n)) if n == 99.99));

        // Check graph connections
        let customer_connections = memories.get_connected("customers").await;
        assert!(customer_connections.contains(&"orders".to_string()));

        let order_connections = memories.get_connected("orders").await;
        assert!(order_connections.contains(&"payments".to_string()));

        // Test complex graph query
        let full_graph = memories.query_graph("customers", 3).await;
        assert!(full_graph.contains(&"customers".to_string()));
        assert!(full_graph.contains(&"orders".to_string()));
        assert!(full_graph.contains(&"payments".to_string()));
    }
}
