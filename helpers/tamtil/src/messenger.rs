//! Messenger module - Zero-copy communication between actors
//! 
//! The messenger handles transparent communication between actors, using in-memory
//! zero-copy action passing when actors run on the same machine, and network
//! serialization when actors are distributed across nodes.

use crate::core::{<PERSON><PERSON>Erro<PERSON>, <PERSON><PERSON>R<PERSON><PERSON>, ActorId};
use rkyv::{Archive, api::high::{to_bytes, from_bytes}, rancor::<PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>};
use std::{collections::HashMap, sync::Arc};
use tokio::sync::RwLock;

/// Message envelope for actor communication
#[derive(Debug, Clone, Archive, rkyv::Serialize, rkyv::Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub struct Message {
    pub from: ActorId,
    pub to: ActorId,
    pub payload: Vec<u8>,
    pub message_type: MessageType,
    pub timestamp: u64,
}

/// Types of messages in the system
#[derive(Debug, Clone, Archive, rkyv::Serialize, rkyv::Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum MessageType {
    /// Fire-and-forget message
    Tell,
    /// Request-response message
    Ask { request_id: u64 },
    /// Response to an Ask message
    Response { request_id: u64 },
    /// System control message
    Control,
}

/// Routing information for actors
#[derive(Debug, Clone)]
pub struct ActorRoute {
    pub actor_id: ActorId,
    pub node_id: String,
    pub is_local: bool,
}

/// The messenger handles all inter-actor communication
pub struct Messenger {
    /// Local routing table for actors on this node
    local_routes: Arc<RwLock<HashMap<ActorId, ActorRoute>>>,
    /// Node identifier for this instance
    node_id: String,
    /// Message queue for local delivery
    local_queue: Arc<RwLock<Vec<Message>>>,
}

impl Messenger {
    /// Create a new messenger for the given node
    pub fn new(node_id: String) -> Self {
        Self {
            local_routes: Arc::new(RwLock::new(HashMap::new())),
            node_id,
            local_queue: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// Register a local actor with the messenger
    pub async fn register_local_actor(&self, actor_id: ActorId) -> TamtilResult<()> {
        let route = ActorRoute {
            actor_id: actor_id.clone(),
            node_id: self.node_id.clone(),
            is_local: true,
        };

        let mut routes = self.local_routes.write().await;
        routes.insert(actor_id.clone(), route);
        
        tracing::debug!("Registered local actor: {}", actor_id.as_str());
        Ok(())
    }

    /// Unregister an actor from the messenger
    pub async fn unregister_actor(&self, actor_id: &ActorId) -> TamtilResult<()> {
        let mut routes = self.local_routes.write().await;
        routes.remove(actor_id);
        
        tracing::debug!("Unregistered actor: {}", actor_id.as_str());
        Ok(())
    }

    /// Send a message between actors
    pub async fn send_message(&self, message: Message) -> TamtilResult<()> {
        let routes = self.local_routes.read().await;
        
        if let Some(route) = routes.get(&message.to) {
            if route.is_local {
                // Local delivery - use zero-copy in-memory passing
                self.deliver_local_message(message).await?;
            } else {
                // Remote delivery - serialize and send over network
                self.deliver_remote_message(message).await?;
            }
        } else {
            return Err(TamtilError::ActorNotFound {
                id: message.to.as_str().to_string(),
            });
        }

        Ok(())
    }

    /// Deliver a message to a local actor (zero-copy)
    async fn deliver_local_message(&self, message: Message) -> TamtilResult<()> {
        // For now, just add to local queue
        // In a full implementation, this would directly invoke the actor
        let mut queue = self.local_queue.write().await;
        queue.push(message);
        
        tracing::debug!("Delivered message locally");
        Ok(())
    }

    /// Deliver a message to a remote actor (network)
    async fn deliver_remote_message(&self, message: Message) -> TamtilResult<()> {
        // Serialize the message for network transmission
        let serialized = to_bytes::<RancorError>(&message)
            .map_err(|e| TamtilError::Serialization {
                message: format!("Failed to serialize message: {}", e)
            })?;

        // In a full implementation, this would:
        // 1. Establish network connection to target node
        // 2. Send serialized message
        // 3. Handle network errors and retries
        
        tracing::debug!("Would send {} bytes to remote actor", serialized.len());
        Ok(())
    }

    /// Create a tell message
    pub fn create_tell_message(
        from: ActorId,
        to: ActorId,
        payload: Vec<u8>,
    ) -> Message {
        Message {
            from,
            to,
            payload,
            message_type: MessageType::Tell,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64,
        }
    }

    /// Create an ask message
    pub fn create_ask_message(
        from: ActorId,
        to: ActorId,
        payload: Vec<u8>,
        request_id: u64,
    ) -> Message {
        Message {
            from,
            to,
            payload,
            message_type: MessageType::Ask { request_id },
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64,
        }
    }

    /// Create a response message
    pub fn create_response_message(
        from: ActorId,
        to: ActorId,
        payload: Vec<u8>,
        request_id: u64,
    ) -> Message {
        Message {
            from,
            to,
            payload,
            message_type: MessageType::Response { request_id },
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64,
        }
    }

    /// Get pending local messages (for testing/debugging)
    pub async fn get_pending_messages(&self) -> Vec<Message> {
        let queue = self.local_queue.read().await;
        queue.clone()
    }

    /// Clear pending messages (for testing)
    pub async fn clear_pending_messages(&self) -> TamtilResult<()> {
        let mut queue = self.local_queue.write().await;
        queue.clear();
        Ok(())
    }

    /// Get the number of registered actors
    pub async fn actor_count(&self) -> usize {
        let routes = self.local_routes.read().await;
        routes.len()
    }

    /// Check if an actor is registered locally
    pub async fn is_local_actor(&self, actor_id: &ActorId) -> bool {
        let routes = self.local_routes.read().await;
        routes.get(actor_id).map_or(false, |route| route.is_local)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_messenger_registration() {
        let messenger = Messenger::new("node1".to_string());
        let actor_id = ActorId::new("test_actor");

        messenger.register_local_actor(actor_id.clone()).await.unwrap();
        assert!(messenger.is_local_actor(&actor_id).await);
        assert_eq!(messenger.actor_count().await, 1);

        messenger.unregister_actor(&actor_id).await.unwrap();
        assert!(!messenger.is_local_actor(&actor_id).await);
        assert_eq!(messenger.actor_count().await, 0);
    }

    #[tokio::test]
    async fn test_message_creation() {
        let from = ActorId::new("actor1");
        let to = ActorId::new("actor2");
        let payload = vec![1, 2, 3, 4];

        let tell_msg = Messenger::create_tell_message(
            from.clone(),
            to.clone(),
            payload.clone(),
        );

        match tell_msg.message_type {
            MessageType::Tell => {},
            _ => panic!("Expected Tell message type"),
        }

        let ask_msg = Messenger::create_ask_message(
            from.clone(),
            to.clone(),
            payload.clone(),
            123,
        );

        match ask_msg.message_type {
            MessageType::Ask { request_id } => assert_eq!(request_id, 123),
            _ => panic!("Expected Ask message type"),
        }
    }

    #[tokio::test]
    async fn test_local_message_delivery() {
        let messenger = Messenger::new("node1".to_string());
        let from = ActorId::new("actor1");
        let to = ActorId::new("actor2");

        // Register the target actor
        messenger.register_local_actor(to.clone()).await.unwrap();

        let message = Messenger::create_tell_message(
            from,
            to,
            vec![1, 2, 3],
        );

        messenger.send_message(message).await.unwrap();

        let pending = messenger.get_pending_messages().await;
        assert_eq!(pending.len(), 1);
    }
}
