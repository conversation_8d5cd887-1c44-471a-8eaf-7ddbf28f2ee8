//! Example actors for testing and demonstration
//! 
//! This module contains example actors that showcase TAMTIL's capabilities
//! and serve as templates for building real applications.

use crate::core::{Re<PERSON>, TamtilR<PERSON>ult, TamtilError, MemoryOperation, MemoryValue};
use crate::{Actor, Actors, Memories};
use async_trait::async_trait;
use rkyv::{Archive, api::high::to_bytes, rancor::Error as RancorError};

/// Example counter action
#[derive(Debug, <PERSON>lone, Archive, rkyv::Serialize, rkyv::Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum CounterAction {
    Increment,
    Decrement,
    GetValue,
    SetValue(i32),
}

/// Example counter reaction
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Archive, rkyv::Serialize, rkyv::Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub struct CounterReaction {
    pub old_value: i32,
    pub new_value: i32,
    pub operation: String,
}

impl Reaction for CounterReaction {
    fn remember(&self) -> Vec<MemoryOperation> {
        vec![
            MemoryOperation::Update {
                key: "counter_value".to_string(),
                value: MemoryValue::number(self.new_value as f64),
            },
            MemoryOperation::Create {
                key: format!("counter_operation_{}", std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap()
                    .as_secs()),
                value: MemoryValue::string(&self.operation),
            }
        ]
    }
}

/// Example counter actor
pub struct CounterActor {
    value: i32,
}

impl CounterActor {
    pub fn new(initial_value: i32) -> Self {
        Self { value: initial_value }
    }
}

#[async_trait]
impl Actor for CounterActor {
    type Action = CounterAction;
    type Reaction = CounterReaction;

    async fn act(&mut self, action: Self::Action, _actors: &Actors, _memories: &Memories) -> TamtilResult<Vec<u8>> {
        let old_value = self.value;

        let (new_value, operation) = match action {
            CounterAction::Increment => {
                self.value += 1;
                (self.value, "increment".to_string())
            }
            CounterAction::Decrement => {
                self.value -= 1;
                (self.value, "decrement".to_string())
            }
            CounterAction::GetValue => {
                (self.value, "get_value".to_string())
            }
            CounterAction::SetValue(val) => {
                self.value = val;
                (self.value, format!("set_value({})", val))
            }
        };

        let reaction = CounterReaction {
            old_value,
            new_value,
            operation,
        };

        // Serialize the reaction using rkyv
        let reaction_bytes = to_bytes::<RancorError>(&reaction)
            .map_err(|e| TamtilError::Serialization {
                message: format!("Failed to serialize reaction: {}", e)
            })?;

        Ok(reaction_bytes.to_vec())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::actor::ActorHandle;

    #[tokio::test]
    async fn test_counter_actor_basic_operations() {
        let memories = Memories::new("test").await.unwrap();
        let actors = Actors::new(memories.clone());
        
        let mut actor = CounterActor::new(10);

        // Test increment
        let reaction_bytes = actor.act(CounterAction::Increment, &actors, &memories).await
            .expect("Failed to increment");

        let archived_reaction = ActorHandle::<CounterActor>::access_reaction::<CounterReaction>(&reaction_bytes)
            .expect("Failed to access reaction");

        assert_eq!(archived_reaction.old_value, 10);
        assert_eq!(archived_reaction.new_value, 11);
        assert_eq!(archived_reaction.operation.as_str(), "increment");

        // Test set value
        let reaction_bytes = actor.act(CounterAction::SetValue(100), &actors, &memories).await
            .expect("Failed to set value");

        let archived_reaction = ActorHandle::<CounterActor>::access_reaction::<CounterReaction>(&reaction_bytes)
            .expect("Failed to access reaction");

        assert_eq!(archived_reaction.old_value, 11);
        assert_eq!(archived_reaction.new_value, 100);
        assert_eq!(archived_reaction.operation.as_str(), "set_value(100)");
    }

    #[test]
    fn test_counter_reaction_memory_operations() {
        let reaction = CounterReaction {
            old_value: 1,
            new_value: 2,
            operation: "increment".to_string(),
        };

        let memory_ops = reaction.remember();
        assert_eq!(memory_ops.len(), 2);

        // Check the counter value update
        match &memory_ops[0] {
            MemoryOperation::Update { key, value } => {
                assert_eq!(key, "counter_value");
                match value {
                    MemoryValue::Number(n) => assert_eq!(*n, 2.0),
                    _ => panic!("Expected number value"),
                }
            }
            _ => panic!("Expected Update operation"),
        }

        // Check the operation log
        match &memory_ops[1] {
            MemoryOperation::Create { key, value } => {
                assert!(key.starts_with("counter_operation_"));
                match value {
                    MemoryValue::String(s) => assert_eq!(s, "increment"),
                    _ => panic!("Expected string value"),
                }
            }
            _ => panic!("Expected Create operation"),
        }
    }
}
