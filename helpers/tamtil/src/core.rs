//! Core types and traits for TAMTIL
//! 
//! This module contains the fundamental types and traits that are used
//! throughout the TAMTIL actor system.

use rkyv::{Archive, Deserialize, Serialize};
use std::fmt;
use thiserror::Error;

/// TAMTIL's error system is designed for both development and production.
/// Each error provides enough context for debugging while being efficient.
#[derive(Error, Debug, Clone)]
pub enum TamtilError {
    #[error("Actor not found: {id}")]
    ActorNotFound { id: String },

    #[error("Storage error: {message}")]
    Storage { message: String },

    #[error("Communication error: {message}")]
    Communication { message: String },

    #[error("Serialization failed: {message}")]
    Serialization { message: String },

    #[error("Deserialization failed: {message}")]
    Deserialization { message: String },

    #[error("Invalid actor address: {address}")]
    InvalidAddress { address: String },

    #[error("Actor {id} panicked: {panic_message}")]
    ActorPanic { id: String, panic_message: String },

    #[error("Memory corruption detected at offset {offset}")]
    MemoryCorruption { offset: u64 },
}

/// Result type for all TAMTIL operations
pub type TamtilResult<T> = Result<T, TamtilError>;

/// Actor addresses follow URL patterns: platform.com/context/actor/child
/// This enables natural distribution and fault isolation.
#[derive(Debug, Clone, PartialEq, Eq, Hash, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub struct ActorId(String);

impl ActorId {
    /// Create a new actor ID
    pub fn new(id: impl Into<String>) -> Self {
        Self(id.into())
    }

    /// Get the string representation
    pub fn as_str(&self) -> &str {
        &self.0
    }

    /// Create a child actor ID
    pub fn child(&self, name: impl Into<String>) -> Self {
        Self(format!("{}/{}", self.0, name.into()))
    }

    /// Get the parent actor ID
    pub fn parent(&self) -> Option<Self> {
        self.0.rfind('/').map(|pos| Self(self.0[..pos].to_string()))
    }

    /// Get the depth in the hierarchy (0 = root)
    pub fn depth(&self) -> usize {
        if self.0.is_empty() { 0 } else { self.0.matches('/').count() }
    }

    /// Check if this is a child of another actor
    pub fn is_child_of(&self, parent: &ActorId) -> bool {
        self.0.starts_with(parent.as_str())
            && self.0.len() > parent.0.len()
            && self.0.chars().nth(parent.0.len()) == Some('/')
    }
}

impl From<&str> for ActorId {
    fn from(s: &str) -> Self {
        Self(s.to_string())
    }
}

impl From<String> for ActorId {
    fn from(s: String) -> Self {
        Self(s)
    }
}

/// Memory values in TAMTIL - pure rkyv zero-copy storage
/// All data is stored as rkyv bytes and accessed via zero-copy
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct MemoryValue {
    /// Raw rkyv serialized bytes - zero-copy access only
    pub bytes: Vec<u8>,
}

impl MemoryValue {
    /// Create MemoryValue from any rkyv-serializable type
    pub fn new<T>(value: &T) -> crate::TamtilResult<Self>
    where
        T: rkyv::Serialize<rkyv::rancor::Strategy<rkyv::ser::Serializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'static>, rkyv::ser::sharing::Share>, rkyv::rancor::Error>>
    {
        let bytes = rkyv::to_bytes(value)
            .map_err(|e| crate::TamtilError::Serialization {
                message: format!("Failed to serialize to MemoryValue: {}", e)
            })?;
        Ok(Self { bytes: bytes.to_vec() })
    }

    /// Create from raw rkyv bytes
    pub fn from_bytes(bytes: Vec<u8>) -> Self {
        Self { bytes }
    }

    /// Simple convenience methods for common types
    pub fn string(s: &str) -> crate::TamtilResult<Self> {
        Self::new(&s.to_string())
    }

    pub fn number(n: f64) -> crate::TamtilResult<Self> {
        Self::new(&n)
    }

    pub fn boolean(b: bool) -> crate::TamtilResult<Self> {
        Self::new(&b)
    }
}

impl fmt::Display for MemoryValue {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{} rkyv bytes", self.bytes.len())
    }
}

/// Memory operations represent state changes that can be persisted.
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub enum MemoryOperation {
    Create { key: String, value: MemoryValue },
    Update { key: String, value: MemoryValue },
    Delete { key: String },
    Link { from: String, to: String, relation: String },
    Unlink { from: String, to: String, relation: String },
}

/// The Action trait - actions contain the logic and produce reactions
pub trait Action: Archive + Send + Sync + 'static {
    type Reaction: Reaction;

    /// Execute the action and produce a reaction
    /// Use actors.actor(id).act() and memories.remember()/recall() for state management
    fn act(self, actors: &crate::Actors, memories: &crate::Memories) -> impl std::future::Future<Output = crate::TamtilResult<Self::Reaction>> + Send
    where
        Self: Send;
}

/// The Reaction trait is central to TAMTIL's design.
/// Every actor action produces a reaction that can be remembered.
pub trait Reaction: Archive + Send + Sync + 'static {
    /// Convert this reaction into memory operations for persistence
    fn remember(&self) -> Vec<MemoryOperation>;
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_actor_id_hierarchy() {
        let root = ActorId::new("platform.local");
        let child = root.child("web");
        let grandchild = child.child("session");

        assert_eq!(root.as_str(), "platform.local");
        assert_eq!(child.as_str(), "platform.local/web");
        assert_eq!(grandchild.as_str(), "platform.local/web/session");

        assert_eq!(root.depth(), 0);
        assert_eq!(child.depth(), 1);
        assert_eq!(grandchild.depth(), 2);

        assert!(child.is_child_of(&root));
        assert!(grandchild.is_child_of(&child));
        assert!(grandchild.is_child_of(&root));
        assert!(!root.is_child_of(&child));
    }

    #[test]
    fn test_memory_value_serialization() {
        use rkyv::{api::high::{to_bytes, from_bytes}, rancor::Error as RancorError};

        let value = MemoryValue::string("test");
        let bytes = to_bytes::<RancorError>(&value).expect("Failed to serialize");
        let deserialized: MemoryValue = from_bytes::<MemoryValue, RancorError>(&bytes).expect("Failed to deserialize");
        assert_eq!(value, deserialized);
    }

    #[test]
    fn test_memory_operation_serialization() {
        use rkyv::{api::high::{to_bytes, from_bytes}, rancor::Error as RancorError};

        let op = MemoryOperation::Create {
            key: "test_key".to_string(),
            value: MemoryValue::number(42.0),
        };
        let bytes = to_bytes::<RancorError>(&op).expect("Failed to serialize");
        let deserialized: MemoryOperation = from_bytes::<MemoryOperation, RancorError>(&bytes).expect("Failed to deserialize");

        match (&op, &deserialized) {
            (MemoryOperation::Create { key: k1, value: v1 }, MemoryOperation::Create { key: k2, value: v2 }) => {
                assert_eq!(k1, k2);
                assert_eq!(v1, v2);
            }
            _ => panic!("Deserialization changed operation type"),
        }
    }
}
