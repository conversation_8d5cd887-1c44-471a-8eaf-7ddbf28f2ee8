//! Memories module - Simplified remember/recall pattern
//! 
//! This module provides a simplified memory system that maintains the remember/recall
//! pattern while using a basic in-memory HashMap for storage. The complex interner
//! and disk components are simplified to get a working version first.

use crate::{Tam<PERSON>Result, MemoryValue, MemoryOperation};
use std::{collections::HashMap, sync::Arc};
use tokio::sync::RwLock;

/// Simplified memories system with in-memory storage
/// This maintains the remember/recall API while using simple HashMap storage
#[derive(Clone)]
pub struct Memories {
    storage: Arc<RwLock<HashMap<String, MemoryValue>>>,
}

impl Memories {
    /// Create a new simplified memories system
    pub async fn new(_storage_path: impl AsRef<std::path::Path>) -> TamtilResult<Self> {
        // For now, ignore the storage path and use in-memory storage
        // This simplifies the implementation while keeping the API
        Ok(Self {
            storage: Arc::new(RwLock::new(HashMap::new())),
        })
    }

    /// Remember (persist) memory operations
    /// This processes the operations and updates the in-memory storage
    pub async fn remember(&self, operations: Vec<MemoryOperation>) -> TamtilResult<()> {
        let mut storage = self.storage.write().await;

        for operation in operations {
            match operation {
                MemoryOperation::Create { key, value } | MemoryOperation::Update { key, value } => {
                    storage.insert(key, value);
                    tracing::debug!("Remembered key: {}", storage.len());
                }
                MemoryOperation::Delete { key } => {
                    storage.remove(&key);
                    tracing::debug!("Deleted key: {}", key);
                }
                MemoryOperation::Link { from, to, relation } => {
                    // For now, store links as special keys
                    // In a full implementation, this would use a graph structure
                    let link_key = format!("link:{}:{}:{}", from, relation, to);
                    storage.insert(link_key, MemoryValue::boolean(true));
                    tracing::debug!("Created link: {} -[{}]-> {}", from, relation, to);
                }
                MemoryOperation::Unlink { from, to, relation } => {
                    let link_key = format!("link:{}:{}:{}", from, relation, to);
                    storage.remove(&link_key);
                    tracing::debug!("Removed link: {} -[{}]-> {}", from, relation, to);
                }
            }
        }

        Ok(())
    }

    /// Recall (retrieve) a memory value
    /// This provides the unified API for all memory access
    pub async fn recall(&self, key: &str) -> Option<MemoryValue> {
        let storage = self.storage.read().await;
        storage.get(key).cloned()
    }

    /// List all memory keys
    pub async fn keys(&self) -> Vec<String> {
        let storage = self.storage.read().await;
        storage.keys().cloned().collect()
    }

    /// Check if a link exists between two nodes
    pub async fn has_link(&self, from: &str, to: &str, relation: &str) -> bool {
        let link_key = format!("link:{}:{}:{}", from, relation, to);
        self.recall(&link_key).await.is_some()
    }

    /// Get all outgoing links from a node
    pub async fn get_outgoing_links(&self, from: &str) -> Vec<(String, String)> {
        let storage = self.storage.read().await;
        let prefix = format!("link:{}:", from);
        
        storage.keys()
            .filter(|key| key.starts_with(&prefix))
            .filter_map(|key| {
                let parts: Vec<&str> = key.split(':').collect();
                if parts.len() == 4 && parts[0] == "link" && parts[1] == from {
                    Some((parts[2].to_string(), parts[3].to_string())) // (relation, to)
                } else {
                    None
                }
            })
            .collect()
    }

    /// Get all incoming links to a node
    pub async fn get_incoming_links(&self, to: &str) -> Vec<(String, String)> {
        let storage = self.storage.read().await;
        let suffix = format!(":{}", to);
        
        storage.keys()
            .filter(|key| key.starts_with("link:") && key.ends_with(&suffix))
            .filter_map(|key| {
                let parts: Vec<&str> = key.split(':').collect();
                if parts.len() == 4 && parts[0] == "link" && parts[3] == to {
                    Some((parts[1].to_string(), parts[2].to_string())) // (from, relation)
                } else {
                    None
                }
            })
            .collect()
    }

    /// Clear all memories (useful for testing)
    pub async fn clear(&self) -> TamtilResult<()> {
        let mut storage = self.storage.write().await;
        storage.clear();
        tracing::debug!("Cleared all memories");
        Ok(())
    }

    /// Get the total number of stored items
    pub async fn len(&self) -> usize {
        let storage = self.storage.read().await;
        storage.len()
    }

    /// Check if the memory store is empty
    pub async fn is_empty(&self) -> bool {
        let storage = self.storage.read().await;
        storage.is_empty()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::MemoryValue;

    #[tokio::test]
    async fn test_basic_remember_recall() {
        let memories = Memories::new("test").await.unwrap();
        
        let operations = vec![
            MemoryOperation::Create {
                key: "test_key".to_string(),
                value: MemoryValue::string("test_value"),
            }
        ];
        
        memories.remember(operations).await.unwrap();
        
        let recalled = memories.recall("test_key").await;
        assert!(recalled.is_some());
        
        if let Some(MemoryValue::String(s)) = recalled {
            assert_eq!(s, "test_value");
        } else {
            panic!("Expected string value");
        }
    }

    #[tokio::test]
    async fn test_links() {
        let memories = Memories::new("test").await.unwrap();
        
        let operations = vec![
            MemoryOperation::Link {
                from: "node1".to_string(),
                to: "node2".to_string(),
                relation: "connects_to".to_string(),
            }
        ];
        
        memories.remember(operations).await.unwrap();
        
        assert!(memories.has_link("node1", "node2", "connects_to").await);
        assert!(!memories.has_link("node2", "node1", "connects_to").await);
        
        let outgoing = memories.get_outgoing_links("node1").await;
        assert_eq!(outgoing.len(), 1);
        assert_eq!(outgoing[0], ("connects_to".to_string(), "node2".to_string()));
    }

    #[tokio::test]
    async fn test_update_and_delete() {
        let memories = Memories::new("test").await.unwrap();
        
        // Create
        let operations = vec![
            MemoryOperation::Create {
                key: "counter".to_string(),
                value: MemoryValue::number(1.0),
            }
        ];
        memories.remember(operations).await.unwrap();
        
        // Update
        let operations = vec![
            MemoryOperation::Update {
                key: "counter".to_string(),
                value: MemoryValue::number(2.0),
            }
        ];
        memories.remember(operations).await.unwrap();
        
        let recalled = memories.recall("counter").await;
        if let Some(MemoryValue::Number(n)) = recalled {
            assert_eq!(n, 2.0);
        } else {
            panic!("Expected number value");
        }
        
        // Delete
        let operations = vec![
            MemoryOperation::Delete {
                key: "counter".to_string(),
            }
        ];
        memories.remember(operations).await.unwrap();
        
        assert!(memories.recall("counter").await.is_none());
    }
}
