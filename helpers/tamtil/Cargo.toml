[package]
name = "tamtil"
version = "0.1.0"
edition.workspace = true
description = "High-performance distributed actor system with zero-copy memory management"
license = "MIT OR Apache-2.0"
repository = "https://github.com/your-org/tutlayt"
homepage = "https://github.com/your-org/tutlayt"
documentation = "https://docs.rs/tamtil"
readme = "README.md"
keywords = ["actor", "distributed", "memory", "rkyv", "performance"]
categories = ["concurrency", "network-programming", "data-structures"]
authors = ["Tamtil Team <<EMAIL>>"]

# Feature flags for production deployment
[features]
default = []
metrics = ["dep:prometheus"]
health-checks = ["dep:hyper"]

[dependencies]
tokio = { workspace = true, features = ["full"] }
tracing = { workspace = true }
tracing-subscriber = { workspace = true, features = ["env-filter", "json"] }
thiserror = { workspace = true }
async-trait = { workspace = true }
rkyv = { workspace = true, features = ["bytecheck"] }
uuid = { workspace = true, features = ["v4"] }
serde = { workspace = true, features = ["derive"] } # Required by OmniPaxos
lazy_static = { workspace = true }
chrono = { workspace = true, features = ["serde"] }
bincode = { workspace = true }

crc32fast.workspace = true
blake3.workspace = true
crossbeam.workspace = true
dashmap.workspace = true
futures.workspace = true
num_cpus.workspace = true

# Distributed consensus and networking
omnipaxos.workspace = true
omnipaxos_storage.workspace = true
quinn.workspace = true
tokio-util.workspace = true
rustls.workspace = true
rcgen.workspace = true
webpki-roots.workspace = true
rand.workspace = true
bytes.workspace = true
base64.workspace = true

# Optional dependencies for features
prometheus = { workspace = true, optional = true }
hyper = { workspace = true, optional = true }
memmap2 = "0.9.5"



[dev-dependencies]
tokio-test = { workspace = true }
tempfile = { workspace = true }
tracing-subscriber = { workspace = true, features = ["env-filter"] }
criterion = { workspace = true, features = ["html_reports", "async_tokio"] }
rusqlite.workspace = true
sled.workspace = true
redb.workspace = true
clap = { version = "4.0", features = ["derive"] }

[[example]]
name = "todoapp"
path = "examples/todoapp.rs"

# Benchmarks will be added in Phase 3: World-Class Performance Optimization
# After core implementation is 100% working and tested



