//! Context module - Service-level concerns and visual foundation
//! 
//! Context actors handle service-level concerns including visual foundation setup.
//! They provide the foundational visual infrastructure and manage service-specific
//! actor hierarchies within the platform.

use crate::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActorId, Actor, Actors, Memories, Reaction, MemoryOperation, MemoryValue};
use async_trait::async_trait;
use rkyv::{Archive, api::high::to_bytes, rancor::Error as RancorError};
use std::fmt;

/// Context actions for managing service-level concerns
#[derive(Debug, Clone, Archive, rkyv::Serialize, rkyv::Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum ContextAction {
    /// Start the context and initialize visual foundation
    Start,
    /// Stop the context and cleanup resources
    Stop,
    /// Setup visual foundation (htmx, tailwind, etc.)
    SetupVisualFoundation,
    /// Create a child actor within this context
    SpawnChildActor { actor_type: String, actor_id: String },
    /// Get context status
    GetStatus,
}

/// Context reactions for service-level responses
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Archive, rkyv::Serialize, rkyv::Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub struct ContextReaction {
    pub context_id: String,
    pub operation: String,
    pub status: String,
    pub message: String,
    pub visual_foundation_ready: bool,
}

impl Reaction for ContextReaction {
    fn remember(&self) -> Vec<MemoryOperation> {
        vec![
            MemoryOperation::Update {
                key: format!("context:{}", self.context_id),
                value: MemoryValue::string(&self.status),
            },
            MemoryOperation::Create {
                key: format!("context:{}:operation", self.context_id),
                value: MemoryValue::string(&self.operation),
            },
            MemoryOperation::Update {
                key: format!("context:{}:visual_ready", self.context_id),
                value: MemoryValue::boolean(self.visual_foundation_ready),
            }
        ]
    }
}

/// Context actor that manages service-level concerns
pub struct ContextActor {
    id: String,
    status: String,
    visual_foundation_ready: bool,
    child_actors: Vec<String>,
}

impl ContextActor {
    pub fn new(id: String) -> Self {
        Self {
            id,
            status: "created".to_string(),
            visual_foundation_ready: false,
            child_actors: Vec::new(),
        }
    }

    /// Setup visual foundation with htmx, tailwind, and _hyperscript
    async fn setup_visual_foundation(&mut self) -> TamtilResult<()> {
        // In a full implementation, this would:
        // 1. Initialize htmx endpoints
        // 2. Setup tailwind CSS compilation
        // 3. Configure _hyperscript behaviors
        // 4. Create base HTML templates
        
        tracing::info!("Setting up visual foundation for context {}", self.id);
        self.visual_foundation_ready = true;
        Ok(())
    }

    /// Start the context and all its services
    async fn start(&mut self, actors: &Actors, memories: &Memories) -> TamtilResult<()> {
        tracing::info!("Starting context {}", self.id);
        
        // Setup visual foundation first
        self.setup_visual_foundation().await?;
        
        // Initialize context state in memories
        let operations = vec![
            MemoryOperation::Create {
                key: format!("context:{}", self.id),
                value: MemoryValue::string("started"),
            },
            MemoryOperation::Create {
                key: format!("context:{}:visual_ready", self.id),
                value: MemoryValue::boolean(true),
            }
        ];
        
        memories.remember(operations).await?;
        self.status = "started".to_string();
        
        Ok(())
    }

    /// Stop the context and cleanup
    async fn stop(&mut self, actors: &Actors, memories: &Memories) -> TamtilResult<()> {
        tracing::info!("Stopping context {}", self.id);
        
        // In a full implementation, this would:
        // 1. Stop all child actors
        // 2. Cleanup visual resources
        // 3. Close htmx endpoints
        
        let operations = vec![
            MemoryOperation::Update {
                key: format!("context:{}", self.id),
                value: MemoryValue::string("stopped"),
            }
        ];
        
        memories.remember(operations).await?;
        self.status = "stopped".to_string();
        self.visual_foundation_ready = false;
        
        Ok(())
    }
}

#[async_trait]
impl Actor for ContextActor {
    type Action = ContextAction;
    type Reaction = ContextReaction;

    async fn act(&mut self, action: Self::Action, actors: &Actors, memories: &Memories) -> TamtilResult<Vec<u8>> {
        let operation = format!("{:?}", action);
        
        let reaction = match action {
            ContextAction::Start => {
                self.start(actors, memories).await?;
                ContextReaction {
                    context_id: self.id.clone(),
                    operation,
                    status: self.status.clone(),
                    message: "Context started successfully".to_string(),
                    visual_foundation_ready: self.visual_foundation_ready,
                }
            }
            ContextAction::Stop => {
                self.stop(actors, memories).await?;
                ContextReaction {
                    context_id: self.id.clone(),
                    operation,
                    status: self.status.clone(),
                    message: "Context stopped successfully".to_string(),
                    visual_foundation_ready: self.visual_foundation_ready,
                }
            }
            ContextAction::SetupVisualFoundation => {
                self.setup_visual_foundation().await?;
                ContextReaction {
                    context_id: self.id.clone(),
                    operation,
                    status: self.status.clone(),
                    message: "Visual foundation setup complete".to_string(),
                    visual_foundation_ready: self.visual_foundation_ready,
                }
            }
            ContextAction::SpawnChildActor { actor_type, actor_id } => {
                // In a full implementation, this would spawn the actual child actor
                self.child_actors.push(actor_id.clone());
                tracing::info!("Would spawn {} actor with id {}", actor_type, actor_id);
                
                ContextReaction {
                    context_id: self.id.clone(),
                    operation,
                    status: self.status.clone(),
                    message: format!("Child actor {} spawned", actor_id),
                    visual_foundation_ready: self.visual_foundation_ready,
                }
            }
            ContextAction::GetStatus => {
                ContextReaction {
                    context_id: self.id.clone(),
                    operation,
                    status: self.status.clone(),
                    message: format!("Context has {} child actors", self.child_actors.len()),
                    visual_foundation_ready: self.visual_foundation_ready,
                }
            }
        };

        // Remember the reaction
        let memory_ops = reaction.remember();
        memories.remember(memory_ops).await?;

        // Serialize the reaction using rkyv
        let reaction_bytes = to_bytes::<RancorError>(&reaction)
            .map_err(|e| TamtilError::Serialization {
                message: format!("Failed to serialize reaction: {}", e)
            })?;

        Ok(reaction_bytes.to_vec())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::actor::ActorHandle;

    #[tokio::test]
    async fn test_context_lifecycle() {
        let memories = Memories::new("test").await.unwrap();
        let actors = Actors::new(memories.clone());
        
        let mut context = ContextActor::new("web".to_string());
        
        // Test start
        let reaction_bytes = context.act(ContextAction::Start, &actors, &memories).await.unwrap();
        let reaction = ActorHandle::<ContextActor>::access_reaction::<ContextReaction>(&reaction_bytes).unwrap();
        
        assert_eq!(reaction.status.as_str(), "started");
        assert!(reaction.visual_foundation_ready);
        
        // Test status
        let reaction_bytes = context.act(ContextAction::GetStatus, &actors, &memories).await.unwrap();
        let reaction = ActorHandle::<ContextActor>::access_reaction::<ContextReaction>(&reaction_bytes).unwrap();
        
        assert_eq!(reaction.status.as_str(), "started");
        
        // Test stop
        let reaction_bytes = context.act(ContextAction::Stop, &actors, &memories).await.unwrap();
        let reaction = ActorHandle::<ContextActor>::access_reaction::<ContextReaction>(&reaction_bytes).unwrap();
        
        assert_eq!(reaction.status.as_str(), "stopped");
        assert!(!reaction.visual_foundation_ready);
    }
}
